<script lang="ts">
    import { onMount } from 'svelte';
    import { user, authLoading } from '$lib/stores/authStore';
    import { db } from '$lib/firebase';
    import { collection, getDocs } from 'firebase/firestore';

    let testMessage = $state('');
    let loading = $state(true);
    let error = $state('');

    onMount(async () => {
        try {
            // Test Firestore connection
            const testCollection = collection(db, 'test');
            await getDocs(testCollection);
            testMessage = 'Firebase connection successful!';
        } catch (err: any) {
            error = err.message || 'Failed to connect to Firebase';
        } finally {
            loading = false;
        }
    });
</script>

<div class="min-h-screen bg-zinc-900 p-6">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-white mb-6">Firebase Test Page</h1>
        
        <div class="bg-zinc-800 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-semibold text-white mb-4">Authentication Status</h2>
            
            {#if $authLoading}
                <p class="text-zinc-400">Checking authentication status...</p>
            {:else if $user}
                <div class="bg-green-500/20 border border-green-500/50 text-green-300 px-4 py-3 rounded-lg">
                    <p class="font-medium">Authenticated</p>
                    <p class="text-sm mt-1">User ID: {$user.uid}</p>
                    <p class="text-sm">Email: {$user.email}</p>
                </div>
            {:else}
                <div class="bg-yellow-500/20 border border-yellow-500/50 text-yellow-300 px-4 py-3 rounded-lg">
                    <p>Not authenticated</p>
                </div>
            {/if}
        </div>
        
        <div class="bg-zinc-800 rounded-lg p-6">
            <h2 class="text-xl font-semibold text-white mb-4">Firestore Connection Test</h2>
            
            {#if loading}
                <div class="flex items-center text-zinc-400">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Testing connection...
                </div>
            {:else if error}
                <div class="bg-red-500/20 border border-red-500/50 text-red-300 px-4 py-3 rounded-lg">
                    <p class="font-medium">Connection Error</p>
                    <p class="text-sm mt-1">{error}</p>
                </div>
            {:else}
                <div class="bg-green-500/20 border border-green-500/50 text-green-300 px-4 py-3 rounded-lg">
                    <p class="font-medium">{testMessage}</p>
                </div>
            {/if}
        </div>
        
        <div class="mt-6 flex space-x-4">
            <a href="/login" class="px-4 py-2 bg-zinc-700 hover:bg-zinc-600 text-white rounded-lg transition-colors">
                Go to Login
            </a>
            <a href="/register" class="px-4 py-2 bg-zinc-700 hover:bg-zinc-600 text-white rounded-lg transition-colors">
                Go to Register
            </a>
            <a href="/dashboard" class="px-4 py-2 bg-zinc-700 hover:bg-zinc-600 text-white rounded-lg transition-colors">
                Go to Dashboard
            </a>
        </div>
    </div>
</div>
