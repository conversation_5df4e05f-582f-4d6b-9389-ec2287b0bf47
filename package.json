{"name": "gemini-ai-task-planner", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "proxy": "node cors-proxy.js", "dev:with-proxy": "concurrently \"npm run dev\" \"npm run proxy\"", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "test:unit": "vitest", "test": "npm run test:unit -- --run"}, "devDependencies": {"@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/vite": "^4.0.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/svelte": "^5.2.4", "concurrently": "^9.1.2", "cors": "^2.8.5", "express": "^5.1.0", "http-proxy-middleware": "^3.0.5", "jsdom": "^26.0.0", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^4.0.0", "typescript": "^5.0.0", "vite": "^6.2.6", "vitest": "^3.0.0"}, "dependencies": {"firebase": "^11.8.1"}}