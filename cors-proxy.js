// Simple CORS proxy server for development
import express from 'express';
import cors from 'cors';
import { createProxyMiddleware } from 'http-proxy-middleware';

// Enable detailed logging
const DEBUG = true;

const app = express();

// Enable CORS for all routes
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Proxy Firebase Authentication requests
app.use('/auth', createProxyMiddleware({
  target: 'https://identitytoolkit.googleapis.com',
  changeOrigin: true,
  pathRewrite: {
    '^/auth': '/v1'
  },
  onProxyReq: function(proxyReq, req, res) {
    if (DEBUG) {
      console.log('Proxying request to:', req.method, req.url);
      console.log('Request headers:', req.headers);
    }
  },
  onProxyRes: function(proxyRes, req, res) {
    proxyRes.headers['Access-Control-Allow-Origin'] = '*';
    proxyRes.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS';
    proxyRes.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization';

    if (DEBUG) {
      console.log('Proxy response status:', proxyRes.statusCode);
      console.log('Response headers:', proxyRes.headers);
    }
  },
  onError: function(err, req, res) {
    console.error('Proxy error:', err);
    res.writeHead(500, {
      'Content-Type': 'text/plain',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    });
    res.end('Proxy error: ' + err.message);
  }
}));

// Add a test endpoint
app.get('/test', (req, res) => {
  res.json({ message: 'CORS proxy server is working!' });
});

// Start the server
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`CORS proxy server running on port ${PORT}`);
  console.log(`Test the proxy at http://localhost:${PORT}/test`);
  console.log(`Firebase Auth requests should be sent to http://localhost:${PORT}/auth`);
});
